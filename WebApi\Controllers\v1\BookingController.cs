using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.DTOs.Booking;
using Application.Exceptions;
using Application.Features.Bookings.Command;
using Application.Features.Bookings.CreateBooking;
using Application.Features.Bookings.GetBookingById;
using Application.Features.Bookings.GetFareDetails;
using Application.Features.Bookings.GetUserBookings;
using Application.Interfaces.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    public class BookingController : BaseApiController
    {
        private readonly IBookingRepositoryAsync bookingRepositoryAsync;

        public BookingController(IBookingRepositoryAsync bookingRepositoryAsync)
        {
            this.bookingRepositoryAsync = bookingRepositoryAsync;
        }
      
        // GET api/<controller>/5
        [HttpGet("{bookingId}")]  // Validate booking ID format in method
        [Authorize]  // Require authentication
        public async Task<IActionResult> Get(string bookingId)
        {
            // Get authenticated user ID - try both uid and sub (username)
            var userId = User.FindFirst("uid")?.Value ?? User.FindFirst("sub")?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { Message = "User ID not found in token" });
            }

            // Validate input
            if (string.IsNullOrWhiteSpace(bookingId))
            {
                return BadRequest(new { Message = "Booking ID is required" });
            }

            // Validate booking ID format: CY-DDMMYY-NNNNN
            if (!System.Text.RegularExpressions.Regex.IsMatch(bookingId, @"^CY-[0-9]{6}-[0-9]{5}$"))
            {
                return BadRequest(new { Message = "Invalid booking ID format. Expected format: CY-DDMMYY-NNNNN" });
            }

            try
            {
                var query = new GetBookingByIdQuery
                {
                    BookingId = bookingId,
                    RequestingUserId = userId  // Pass user ID for ownership check
                };

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Application.Exceptions.UnauthorizedAccessException)
            {
                return Forbid();
            }
            catch (ApiException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                // Log the error but don't expose internal details
                Console.WriteLine($"Error retrieving booking {bookingId} for user {userId}: {ex.Message}");
                return StatusCode(500, new { Message = "Error retrieving booking details" });
            }
        }

        [HttpGet("PaymentOption")]
        public async Task<IActionResult> Get(decimal bookingId,int PaymentOption)
        {
  
            return Ok();

        }

        // POST api/<controller>
        [HttpPost("NewBooking")]
        [Authorize]
        public async Task<IActionResult> Post(BookingCommand command)
        {
            var isAuthenticated = User.Identity.IsAuthenticated;
            var userName = User.Identity.Name;
            var userId = User.FindFirst("uid")?.Value; // Get the user ID from claims

            Console.WriteLine($"Controller - Is Authenticated: {isAuthenticated}");
            Console.WriteLine($"Controller - User Name: {userName}");
            Console.WriteLine($"Controller - User ID: {userId}");

            // Set the BookingCreatedBy property with the authenticated user's ID
            if (!string.IsNullOrEmpty(userId))
            {
                command.BookingCreatedBy = userId;
            }

            // You can also set other user-related properties if needed
            if (string.IsNullOrEmpty(command.TravelerName) && !string.IsNullOrEmpty(userName))
            {
                command.TravelerName = userName;
            }

            try
            {
                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                // Log the detailed exception
                Console.WriteLine($"Exception in Mediator.Send: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner exception type: {ex.InnerException.GetType().Name}");
                }

                // Return a more detailed error response for debugging
                return StatusCode(500, new
                {
                    Succeeded = false,
                    Message = "Error processing booking command",
                    Error = ex.Message,
                    InnerError = ex.InnerException?.Message,
                    ExceptionType = ex.GetType().Name
                });
            }
        }

        // POST api/<controller>
        [HttpGet("GetFareDetails")]
        // [Authorize]
        public async Task<IActionResult> Get([FromQuery] string pickUpAddressLongLat, [FromQuery] string dropOffAddressLongLat, [FromQuery] string tripType)
        {
            return Ok(await Mediator.Send(new GetFareByParamQuery {PickUpAddressLongLat=pickUpAddressLongLat,DropOffAddressLongLat=dropOffAddressLongLat,TripType=tripType}));
        }

        // POST api/<controller>
        [HttpGet("GetSelectedtRouteCategoryFareDetails")]
        // [Authorize]
        public async Task<IActionResult> Get([FromQuery] string pickUpAddressLongLat, [FromQuery] string dropOffAddressLongLat, [FromQuery] string tripType, [FromQuery] string categoryName)
        {
            return Ok(await Mediator.Send(new GetSelectedRouteCategoryFareDetails { PickUpAddressLongLat = pickUpAddressLongLat, DropOffAddressLongLat = dropOffAddressLongLat, TripType = tripType ,CarCagetgory=categoryName}));
        }


        // POST api/<controller>
        [HttpPost("PaymentStatusUpdate")]
        // [Authorize]
        public async Task<IActionResult> Post([FromBody] CreateBookingPaymentCommand createBookingPaymentCommand)
        {
            return Ok(await Mediator.Send(createBookingPaymentCommand));
        }

        // GET api/<controller>/user-bookings
        [HttpGet("user-bookings")]
        [Authorize]  // Require authentication
        public async Task<IActionResult> GetUserBookings([FromQuery] string cursor = null, [FromQuery] int pageSize = 10)
        {
            // Get authenticated user ID - try both uid and sub (username)
            var userId = User.FindFirst("uid")?.Value ?? User.FindFirst("sub")?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { Message = "User ID not found in token" });
            }

            // Validate input parameters
            if (pageSize <= 0)
            {
                return BadRequest(new { Message = "Page size must be greater than 0" });
            }

            // Limit page size to maximum of 10
            if (pageSize > 10)
            {
                pageSize = 10;
            }

            var query = new GetUserBookingsQuery
            {
                UserId = userId,
                Cursor = cursor,
                PageSize = pageSize
            };

            try
            {
                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Application.Exceptions.UnauthorizedAccessException)
            {
                return Forbid();
            }
            catch (ApiException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                // Log the error but don't expose internal details
                Console.WriteLine($"Error retrieving user bookings for user {userId}: {ex.Message}");
                return StatusCode(500, new { Message = "Error retrieving user bookings" });
            }
        }


    }
}