﻿using Application.DTOs.Booking;
using Application.DTOs.Payment;
using Application.Enums;
using Application.Features.Bookings.Common;
using Application.Interfaces.Repositories;
using Dapper;
using Domain.Entities;
using Domain.Settings;
using Infrastructure.Persistence.Contexts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Razorpay.Api;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Persistence.Repositories
{
    public class BookingRepositoryAsync : IBookingRepositoryAsync
    {

        private readonly IConfiguration configuration;
        private readonly ApplicationDbContext _dbContext;
        private readonly PaymentSettings paymentSettings;
        public BookingRepositoryAsync(IConfiguration configuration, ApplicationDbContext dbContext, IOptions<PaymentSettings> paymentSettings)
        {

            this.configuration = configuration;

            _dbContext = dbContext;
            this.paymentSettings = paymentSettings.Value;
        }

        public async Task<string> AddAsync(RLT_BOOKING entity)
        {
            string bookingId = string.Empty;
            var bookingID = BookingUtility.GenerateBookingID();

            var orderId = RazorPayPaymentOrderCreate(Convert.ToDecimal(entity.Fare), bookingID);

            if (orderId == null)
            {
                return null;
            }

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@Booking_Id", bookingID, DbType.String);
                _params.Add("@PickUpCity", entity.PickUpCity, DbType.String);
                _params.Add("@DropOffCity", entity.DropOffCity, DbType.String);
                _params.Add("@TripType", entity.TripType, DbType.String);
                _params.Add("@CarCategory", entity.CarCategory, DbType.String);
                _params.Add("@Duration", entity.Duration, DbType.String);
                _params.Add("@Distance", entity.Distance, DbType.Decimal);
                _params.Add("@BasicFare", entity.BasicFare, DbType.Decimal);
                _params.Add("@DriverCharge", entity.DriverCharge, DbType.Decimal);
                _params.Add("@Gst", entity.Gst, DbType.Decimal);
                _params.Add("@Fare", entity.Fare, DbType.Decimal);
                _params.Add("@GstFare", entity.GstFare, DbType.Decimal);
                _params.Add("@CouponCode", entity.CouponCode, DbType.String);
                _params.Add("@CouponDiscount", entity.CouponDiscount, DbType.Decimal);
                _params.Add("@PickUpAddress", entity.PickUpAddress, DbType.String);
                _params.Add("@DropOffAddress", entity.DropOffAddress, DbType.String);
                _params.Add("@PickUpDate", entity.PickUpDate, DbType.Date);
                _params.Add("@PickUpTime", entity.PickUpTime, DbType.String);
                _params.Add("@TravelerName", entity.TravelerName, DbType.String);
                _params.Add("@PhoneNumber", entity.PhoneNumber, DbType.String);
                _params.Add("@MailId", entity.MailId, DbType.String);
                _params.Add("@PaymentMode", entity.PaymentMode, DbType.Int32);
                _params.Add("@BookingCreatedBy", entity.BookingCreatedBy, DbType.String);
                _params.Add("@RazorpayPaymentId", entity.RazorpayPaymentId, DbType.String);
                _params.Add("@RazorpayOrderid", orderId, DbType.String);
                _params.Add("@RazorpaySignature", entity.RazorpaySignature, DbType.String);
                _params.Add("@RazorpayStatus", PaymentStatus.Pending, DbType.String);
                _params.Add("@PickUpAddressLongLat", entity.PickUpAddressLongLat, DbType.String);
                _params.Add("@PickUpAddressLongitude", entity.DropOffAddressLongLat, DbType.String);
                _params.Add("@CashAmountToPayDriver", entity.CashAmountToPayDriver, DbType.Decimal);
                _params.Add("@PaymentOption", entity.PaymentOption, DbType.Int32);
                _params.Add("@TollCharge", entity.TollCharge, DbType.Decimal);
                _params.Add("@result", DbType.String, direction: ParameterDirection.Output);
                var result = await connection.ExecuteAsync("usp_Booking_Create", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);
                if (result != null)
                {
                    int Id = _params.Get<Int32>("result");
                    if (Id != null)
                    {
                        bookingId = bookingID;
                    }
                }

                return bookingId;
            }

        }

        public async Task DeleteAsync(RLT_BOOKING entity)
        {
            throw new NotImplementedException();
        }

        public async Task<IReadOnlyList<RLT_BOOKING>> GetAllAsync()
        {
            throw new NotImplementedException();
        }

        public async Task<RLT_BOOKING> GetByUniqueIdAsync(string id)
        {
            var _params = new
            {
                bookingID = id
            };
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                var result = await connection.QueryFirstOrDefaultAsync<RLT_BOOKING>("usp_BookingDetails_Get", _params, commandType: CommandType.StoredProcedure);
                return result;
            }
        }

        public Task<IReadOnlyList<RLT_BOOKING>> GetPagedReponseAsync(int pageNumber, int pageSize)
        {
            throw new NotImplementedException();
        }

        public async Task UpdateAsync(RLT_BOOKING entity)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@BookingId", entity.BookingID, DbType.String);
                _params.Add("@RazorpayPaymentId", entity.RazorpayPaymentId, DbType.String);
                _params.Add("@RazorpayOrderid", entity.RazorpayOrderid, DbType.String);
                _params.Add("@RazorpaySignature", entity.RazorpaySignature ?? "", DbType.String);
                _params.Add("@RazorpayStatus", entity.RazorpayStatus, DbType.String);
                _params.Add("@PaymentType", entity.PaymentType, DbType.String);
                _params.Add("@PartialPaymentAmount", entity.PartialPaymentAmount, DbType.Decimal);
                _params.Add("@RemainingAmountForDriver", entity.RemainingAmountForDriver, DbType.Decimal);

                var result = await connection.ExecuteAsync("usp_Booking_Update_V2", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);
            }
        }


        public async Task<RazorPaymentResponse> RazorPayPaymentSuccess(string paymentId, string razorpayOrderId, string razorpaySignature)
        {
            RazorPaymentResponse razorPaymentResponse = new RazorPaymentResponse();

            string payment_Id = paymentId;
            string razorpay_order_id = razorpayOrderId;

            int paymentStatus = (int)PaymentStatus.Pending;


            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {


                if (paymentId != null)
                {
                    string key = this.paymentSettings.PaymentKey;
                    string secret = this.paymentSettings.PaymentSecret;

                    RazorpayClient client = new RazorpayClient(key, secret);
                    //var paymetStatus = client.Payment.Fetch(razorpayOrderId);

                    Dictionary<string, string> attributes = new Dictionary<string, string>();

                    attributes.Add("razorpay_payment_id", paymentId);
                    attributes.Add("razorpay_order_id", razorpay_order_id);
                    attributes.Add("razorpay_signature", razorpaySignature);
                    Utils.verifyPaymentSignature(attributes);
                }
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@RazorpayPaymentId", paymentId, DbType.String);
                _params.Add("@RazorpayOrderid", razorpay_order_id, DbType.String);
                _params.Add("@RazorpaySignature", razorpaySignature, DbType.String);
                _params.Add("@RazorpayStatus", PaymentStatus.Paid, DbType.String);
                var result = await connection.QueryFirstOrDefaultAsync<RazorPaymentResponse>("usp_Booking_update", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);

                if (result.BookingId != null && result.TransactionId != null)
                {
                    razorPaymentResponse.BookingId = result.BookingId;
                    razorPaymentResponse.TransactionId = result.TransactionId;
                    razorPaymentResponse.PaymentStatus = result.PaymentStatus == Convert.ToString(PaymentStatus.Paid) ? "Paid" : "";
                }

                return result;


            }

        }
        private string RazorPayPaymentOrderCreate(decimal fare, string bookingId)
        {

            int Amount = Convert.ToInt32(fare) * 100;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            Dictionary<string, object> input = new Dictionary<string, object>();
            input.Add("amount", Amount); // this amount should be same as transaction amount
            input.Add("currency", "INR");
            input.Add("receipt", bookingId);
            input.Add("payment_capture", 1);
            string key = this.paymentSettings.PaymentKey;
            string secret = this.paymentSettings.PaymentSecret;
            RazorpayClient client = new RazorpayClient(key, secret);
            Razorpay.Api.Order order = client.Order.Create(input);
            string orderId = order["id"].ToString();

            return orderId;
        }

        public async Task<BookingResponse> AddNewAsync(RLT_BOOKING entity)
        {
            BookingResponse bookingResponse = new BookingResponse();

            string bookingId = string.Empty;
            var bookingID = BookingUtility.GenerateBookingID();

            var orderId = RazorPayPaymentOrderCreate(Convert.ToDecimal(entity.Fare), bookingID);

            if (orderId == null)
            {
                return null;
            }

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@Booking_Id", bookingID, DbType.String);
                _params.Add("@PickUpCity", entity.PickUpCity, DbType.String);
                _params.Add("@DropOffCity", entity.DropOffCity, DbType.String);
                _params.Add("@TripType", entity.TripType, DbType.String);
                _params.Add("@CarCategory", entity.CarCategory, DbType.String);
                _params.Add("@Duration", entity.Duration, DbType.String);
                _params.Add("@Distance", entity.Distance, DbType.Decimal);
                _params.Add("@BasicFare", entity.BasicFare, DbType.Decimal);
                _params.Add("@DriverCharge", entity.DriverCharge, DbType.Decimal);
                _params.Add("@Gst", entity.Gst, DbType.Decimal);
                _params.Add("@Fare", entity.Fare, DbType.Decimal);
                _params.Add("@GstFare", entity.GstFare, DbType.Decimal);
                _params.Add("@CouponCode", entity.CouponCode, DbType.String);
                _params.Add("@CouponDiscount", entity.CouponDiscount, DbType.Decimal);
                _params.Add("@PickUpAddress", entity.PickUpAddress, DbType.String);
                _params.Add("@DropOffAddress", entity.DropOffAddress, DbType.String);
                _params.Add("@PickUpDate", entity.PickUpDate, DbType.Date);
                _params.Add("@PickUpTime", entity.PickUpTime, DbType.String);
                _params.Add("@TravelerName", entity.TravelerName, DbType.String);
                _params.Add("@PhoneNumber", entity.PhoneNumber, DbType.String);
                _params.Add("@MailId", entity.MailId, DbType.String);
                _params.Add("@PaymentMode", entity.PaymentMode, DbType.Int32);
                _params.Add("@BookingCreatedBy", entity.BookingCreatedBy, DbType.String);
                _params.Add("@RazorpayPaymentId", entity.RazorpayPaymentId, DbType.String);
                _params.Add("@RazorpayOrderid", orderId, DbType.String);
                _params.Add("@RazorpaySignature", entity.RazorpaySignature, DbType.String);
                _params.Add("@RazorpayStatus", PaymentStatus.Pending, DbType.String);
                _params.Add("@PickUpAddressLongLat", entity.PickUpAddressLongLat, DbType.String);
                _params.Add("@PickUpAddressLongitude", entity.DropOffAddressLongLat, DbType.String);
                _params.Add("@CashAmountToPayDriver", entity.CashAmountToPayDriver, DbType.Decimal);
                _params.Add("@PaymentOption", entity.PaymentOption, DbType.Int32);
                _params.Add("@TollCharge", entity.TollCharge, DbType.Decimal);
                _params.Add("@PaymentType", entity.PaymentType, DbType.String);
                _params.Add("@PartialPaymentAmount", entity.PartialPaymentAmount, DbType.Decimal);
                _params.Add("@RemainingAmountForDriver", entity.RemainingAmountForDriver, DbType.Decimal);
                _params.Add("@result", DbType.String, direction: ParameterDirection.Output);
                var result = await connection.ExecuteAsync("usp_Booking_Create_V2", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);
                if (result != null)
                {
                    int Id = _params.Get<Int32>("result");
                    if (Id != null)
                    {
                        bookingResponse.BookingId = bookingID;
                        bookingResponse.OrderId = orderId;
                    }
                }

                return bookingResponse;
            }
        }

        public async Task<UserBookingsResponse> GetUserBookingsByUserIdAsync(string userId, string cursor, int pageSize)
        {
            var response = new UserBookingsResponse();

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                // SQL query to get user bookings with pagination
                var sql = @"
                    WITH BookingData AS (
                        SELECT
                            Booking_Id as BookingId,
                            PickUp_City as PickUpCity,
                            DropOff_City as DropOffCity,
                            Trip_Type as TripType,
                            Car_Category as CarCategory,
                            ROUND(Fare, 0) as Fare,  -- Round fare to no decimal places
                            PickUp_Address as PickUpAddress,
                            DropOff_Address as DropOffAddress,
                            PickUp_Date as PickUpDate,
                            PickUp_Time as PickUpTime,
                            Traveler_Name as TravelerName,
                            Phone_Number as PhoneNumber,
                            Razorpay_Status as RazorpayStatus,
                            Created_Date as BookingDate,
                            ROW_NUMBER() OVER (ORDER BY Created_Date DESC) as RowNum
                        FROM RLT_BOOKING
                        WHERE Booking_Created_By = @UserId
                        AND (@Cursor IS NULL OR Created_Date < @CursorDate)
                    )
                    SELECT TOP (@PageSizePlusOne) *
                    FROM BookingData
                    ORDER BY BookingDate DESC";

                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("@UserId", userId, DbType.String);
                parameters.Add("@PageSizePlusOne", pageSize + 1, DbType.Int32);

                DateTime? cursorDate = null;
                if (!string.IsNullOrEmpty(cursor) && DateTime.TryParse(cursor, out DateTime parsedDate))
                {
                    cursorDate = parsedDate;
                }
                parameters.Add("@Cursor", cursor, DbType.String);
                parameters.Add("@CursorDate", cursorDate, DbType.DateTime);

                var bookings = await connection.QueryAsync<UserBookingItem>(sql, parameters);
                var bookingList = bookings.ToList();

                // Check if there are more results
                response.HasMoreResult = bookingList.Count > pageSize;

                // Remove the extra item if we have more results
                if (response.HasMoreResult)
                {
                    bookingList.RemoveAt(bookingList.Count - 1);
                }

                response.Bookings = bookingList;
                response.TotalCount = bookingList.Count;

                // Set next cursor to the last item's booking date
                if (response.HasMoreResult && bookingList.Any())
                {
                    response.NextCursor = bookingList.Last().BookingDate?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                }

                return response;
            }
        }
    }
}
